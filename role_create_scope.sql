-- 角色创建权限配置表
CREATE TABLE IF NOT EXISTS `role_create_scope` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `from_role_id` bigint(20) NOT NULL COMMENT '源角色ID（拥有创建权限的角色）',
    `to_role_id` bigint(20) NOT NULL COMMENT '目标角色ID（允许被创建的角色）',
    `org_id` bigint(20) NOT NULL COMMENT '组织ID',
    `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `updated_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_from_to_org` (`from_role_id`, `to_role_id`, `org_id`),
    KEY `idx_from_role_org` (`from_role_id`, `org_id`),
    KEY `idx_to_role_org` (`to_role_id`, `org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色创建权限配置表';
