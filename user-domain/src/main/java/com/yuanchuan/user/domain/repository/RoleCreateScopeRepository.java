package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.RoleCreateScope;

import java.util.List;
import java.util.Optional;

/**
 * 角色创建权限配置仓储接口
 */
public interface RoleCreateScopeRepository {
    
    /**
     * 保存角色创建权限配置
     *
     * @param roleCreateScope 角色创建权限配置
     * @return 保存后的配置
     */
    RoleCreateScope save(RoleCreateScope roleCreateScope);

    /**
     * 根据ID查询配置
     *
     * @param id 配置ID
     * @return 配置信息
     */
    Optional<RoleCreateScope> findById(Long id);

    /**
     * 根据源角色ID查询可创建的目标角色列表
     *
     * @param fromRoleId 源角色ID
     * @param orgId 组织ID
     * @return 目标角色ID列表
     */
    List<Long> findTargetRoleIdsByFromRoleId(Long fromRoleId, Long orgId);

    /**
     * 根据源角色ID和目标角色ID查询配置
     *
     * @param fromRoleId 源角色ID
     * @param toRoleId 目标角色ID
     * @param orgId 组织ID
     * @return 配置信息
     */
    Optional<RoleCreateScope> findByFromRoleIdAndToRoleId(Long fromRoleId, Long toRoleId, Long orgId);

    /**
     * 根据源角色ID查询所有配置
     *
     * @param fromRoleId 源角色ID
     * @param orgId 组织ID
     * @return 配置列表
     */
    List<RoleCreateScope> findByFromRoleId(Long fromRoleId, Long orgId);

    /**
     * 根据目标角色ID查询所有配置
     *
     * @param toRoleId 目标角色ID
     * @param orgId 组织ID
     * @return 配置列表
     */
    List<RoleCreateScope> findByToRoleId(Long toRoleId, Long orgId);

    /**
     * 删除配置
     *
     * @param id 配置ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据源角色ID删除所有配置
     *
     * @param fromRoleId 源角色ID
     * @param orgId 组织ID
     * @return 是否删除成功
     */
    boolean deleteByFromRoleId(Long fromRoleId, Long orgId);

    /**
     * 批量保存配置
     *
     * @param roleCreateScopes 配置列表
     * @return 保存的配置列表
     */
    List<RoleCreateScope> saveAll(List<RoleCreateScope> roleCreateScopes);
}
