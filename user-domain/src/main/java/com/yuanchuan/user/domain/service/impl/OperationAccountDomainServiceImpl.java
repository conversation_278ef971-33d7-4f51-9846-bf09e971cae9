package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.ResponseCode;
import com.yuanchuan.common.utils.ValidationTaiWanPhoneUtil;
import com.yuanchuan.user.context.enums.AccountStatus;
import com.yuanchuan.user.context.enums.AccountType;
import com.yuanchuan.user.domain.model.BusinessAccount;
import com.yuanchuan.user.domain.model.BusinessAccountRole;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.model.UserChangeLogs;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.repository.BusinessAccountRepository;
import com.yuanchuan.user.domain.repository.BusinessAccountRoleRepository;
import com.yuanchuan.user.domain.repository.RoleRepository;
import com.yuanchuan.user.domain.repository.UserPersonRepository;
import com.yuanchuan.user.domain.service.OperationAccountDomainService;
import com.yuanchuan.user.domain.service.PasswordPolicyService;
import com.yuanchuan.user.domain.service.RoleDomainService;
import com.yuanchuan.user.domain.service.UserChangeLogsService;
import com.yuanchuan.common.context.UserContext;
import com.yuanchuan.common.utils.AuthUserContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 运营账号管理领域服务实现
 */
@Slf4j
@Service
public class OperationAccountDomainServiceImpl implements OperationAccountDomainService {

    @Autowired
    private BusinessAccountRepository businessAccountRepository;

    @Autowired
    private UserPersonRepository userPersonRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private BusinessAccountRoleRepository businessAccountRoleRepository;

    @Autowired
    private UserChangeLogsService userChangeLogsService;

    @Autowired
    private PasswordPolicyService passwordPolicyService;

    @Autowired
    private RoleDomainService roleDomainService;

    // 邮箱格式验证正则
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    @Override
    public List<BusinessAccount> queryOperationAccounts(List<Long> roleIds, Integer accountStatus, String keyword, PageQueryV pageQuery) {
        return businessAccountRepository.queryOperationAccounts(roleIds, accountStatus, keyword, pageQuery);
    }

    @Override
    public Long countOperationAccounts(List<Long> roleIds, Integer accountStatus, String keyword) {
        return businessAccountRepository.countOperationAccounts(roleIds, accountStatus, keyword);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOperationAccount(String name, String employeeCode, String email, String phone,
                                      List<Long> roleIds, String createdBy) {
        // 1. 参数验证
        validateCreateParams(name, employeeCode, email, phone, roleIds);

        // 2. 业务规则验证
        validateBusinessRules(employeeCode, email, phone, null);

        // 3. 验证角色是否存在且有效
        validateRoles(roleIds);

        // 校验手机号或邮箱是否已经存在
        validateEmailOrPhoneExist(email, phone,null);

        try {
            // 4. 创建UserPerson
            UserPerson userPerson = new UserPerson();
            userPerson.setPhone(phone);
            userPerson.setEmail(email);
            userPerson.setSource(PlatformType.ADMIN.getCode()); // 运营账号来源
            userPerson.setCreatedAt(LocalDateTime.now());
            userPerson.setUpdatedAt(LocalDateTime.now());
            userPerson.setCreatedBy(createdBy);
            userPerson.setUpdatedBy(createdBy);
            userPerson.setActive(true);

            userPerson = userPersonRepository.save(userPerson);

            // 5. 创建BusinessAccount
            BusinessAccount businessAccount = new BusinessAccount();
            businessAccount.setUserPersonId(userPerson.getId());
            businessAccount.setAccountName(name);
            businessAccount.setAccountCode(employeeCode);
            businessAccount.setPassword(passwordPolicyService.encryptPassword(phone));
            businessAccount.setAccountType(AccountType.ADMIN.getCode());
            businessAccount.setAccountStatus(AccountStatus.ACTIVE.getCode());
            businessAccount.setCreatedAt(LocalDateTime.now());
            businessAccount.setUpdatedAt(LocalDateTime.now());
            businessAccount.setCreatedBy(createdBy);
            businessAccount.setUpdatedBy(createdBy);
            businessAccount.setActive(true);

            businessAccount = businessAccountRepository.save(businessAccount);

            // 6. 关联角色
            associateRoles(businessAccount.getId(), roleIds, createdBy);

            // 7. 记录变更日志
            recordCreateLog(userPerson.getId(), name, employeeCode, email, phone, roleIds, createdBy);

            log.info("创建运营账号成功，账号ID: {}, 员工编码: {}, 创建人: {}",
                    businessAccount.getId(), employeeCode, createdBy);

            return businessAccount.getId();

        } catch (Exception e) {
            log.error("创建运营账号失败，员工编码: {}, 错误: {}", employeeCode, e.getMessage(), e);
            throw new BusinessException(UsersErrorCode.CREATE_ACCOUNT_FAILED.getCode(), UsersErrorCode.CREATE_ACCOUNT_FAILED.getMsg());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOperationAccount(Long id, String name, String email, String phone,
                                         List<Long> roleIds,Integer status, String updatedBy) {
        // 1. 查询现有账号
        BusinessAccount existingAccount = businessAccountRepository.findById(id)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), "帳號不存在"));

        // 2. 验证账号类型
        if (existingAccount.getAccountType() != AccountType.ADMIN.getCode()) {
            throw new BusinessException(UsersErrorCode.ONLY_MODIFY_ADMIN_ACCOUNT.getCode(), UsersErrorCode.ONLY_MODIFY_ADMIN_ACCOUNT.getMsg());
        }

        // 3. 参数验证
        validateUpdateParams(name, email, phone, roleIds);

        // 4. 业务规则验证（排除当前账号）
        if (StringUtils.hasText(email)) {
            validateBusinessRules(null, email, phone, id);
        }

        // 5. 验证角色
        if (roleIds != null && !roleIds.isEmpty()) {
            validateRoles(roleIds);
        }

        try {
            // 6. 获取UserPerson
            UserPerson userPerson = userPersonRepository.findById(existingAccount.getUserPersonId())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), "用户信息不存在"));

            // 7. 记录变更前的值
            Map<String, Object> oldValues = buildOldValues(userPerson, existingAccount);

            // 8. 更新UserPerson
            boolean userPersonUpdated = false;

            validateEmailOrPhoneExist(email, phone,userPerson.getId());

            if (StringUtils.hasText(email) && !email.equals(userPerson.getEmail())) {
                userPerson.setEmail(email);
                userPersonUpdated = true;
            }
            if (StringUtils.hasText(phone) && !phone.equals(userPerson.getPhone())) {
                userPerson.setPhone(phone);
                userPersonUpdated = true;
            }

            if (userPersonUpdated) {
                userPerson.setUpdatedAt(LocalDateTime.now());
                userPerson.setUpdatedBy(updatedBy);
                userPersonRepository.save(userPerson);
            }

            // 9. 更新BusinessAccount
            existingAccount.setUpdatedAt(LocalDateTime.now());
            existingAccount.setUpdatedBy(updatedBy);
            existingAccount.setAccountName(name);
            existingAccount.setAccountStatus(status);
            businessAccountRepository.save(existingAccount);

            // 10. 更新角色关联
            if (roleIds != null) {
                updateAccountRoles(existingAccount.getId(), roleIds, updatedBy);
            }

            // 11. 记录变更日志
            Map<String, Object> newValues = buildNewValues(userPerson, existingAccount, name, roleIds);
            recordUpdateLog(userPerson.getId(), oldValues, newValues, updatedBy);

            log.info("修改运营账号成功，账号ID: {}, 修改人: {}", id, updatedBy);
            return true;

        } catch (Exception e) {
            log.error("修改运营账号失败，账号ID: {}, 错误: {}", id, e.getMessage(), e);
            throw new BusinessException(UsersErrorCode.MODIFY_ACCOUNT_FAILED.getCode(), UsersErrorCode.MODIFY_ACCOUNT_FAILED.getMsg());
        }
    }

    @Override
    public BusinessAccount getOperationAccountById(Long id) {
        Optional<BusinessAccount> accountOpt = businessAccountRepository.findById(id);
        if (accountOpt.isPresent()) {
            BusinessAccount account = accountOpt.get();
            // 验证是否为运营账号
            if (account.getAccountType() == AccountType.ADMIN.getCode()) {
                return account;
            }
        }
        return null;
    }

    @Override
    public Boolean isEmployeeCodeUnique(String employeeCode, Long excludeId) {
        // 员工编码实际上是accountName，需要检查是否唯一
        Optional<BusinessAccount> existing = businessAccountRepository
                .findByAccountNameAndAccountType(employeeCode, AccountType.ADMIN.getCode());

        if (existing.isPresent()) {
            return excludeId != null && excludeId.equals(existing.get().getId());
        }
        return true;
    }

    @Override
    public Boolean isEmailUnique(String email, Long excludeId) {
        Optional<BusinessAccount> existing = businessAccountRepository
                .findByEmailAndAccountType(email, AccountType.ADMIN.getCode());

        if (existing.isPresent()) {
            return excludeId != null && excludeId.equals(existing.get().getId());
        }
        return true;
    }

    @Override
    public Boolean isPhoneUnique(String phone, Long excludeId) {
        Optional<BusinessAccount> existing = businessAccountRepository
                .findByPhoneAndAccountType(phone, AccountType.ADMIN.getCode());

        if (existing.isPresent()) {
            return excludeId != null && excludeId.equals(existing.get().getId());
        }
        return true;
    }

    /**
     * 验证创建参数
     */
    private void validateCreateParams(String name, String employeeCode, String email, String phone, List<Long> roleIds) {
        if (!StringUtils.hasText(name) || name.length() > 20) {
            throw new BusinessException(UsersErrorCode.NAME_EMPTY_OR_TOO_LONG.getCode(), UsersErrorCode.NAME_EMPTY_OR_TOO_LONG.getMsg());
        }
        if (!StringUtils.hasText(employeeCode)) {
            throw new BusinessException(UsersErrorCode.EMPLOYEE_CODE_EMPTY.getCode(), UsersErrorCode.EMPLOYEE_CODE_EMPTY.getMsg());
        }
        if (!StringUtils.hasText(email) || !EMAIL_PATTERN.matcher(email).matches()) {
            throw new BusinessException(UsersErrorCode.EMAIL_FORMAT_ERROR.getCode(), UsersErrorCode.EMAIL_FORMAT_ERROR.getMsg());
        }
        if (!StringUtils.hasText(phone) || !ValidationTaiWanPhoneUtil.isValidTaiwanPhone(phone)) {
            throw new BusinessException(UsersErrorCode.PHONE_FORMAT_ERROR.getCode(), UsersErrorCode.PHONE_FORMAT_ERROR.getMsg());
        }
        if (roleIds == null || roleIds.isEmpty()) {
            throw new BusinessException(UsersErrorCode.ROLE_EMPTY.getCode(), UsersErrorCode.ROLE_EMPTY.getMsg());
        }
    }

    /**
     * 验证修改参数
     */
    private void validateUpdateParams(String name, String email, String phone, List<Long> roleIds) {
        if (StringUtils.hasText(name) && name.length() > 20) {
            throw new BusinessException(UsersErrorCode.NAME_EMPTY_OR_TOO_LONG.getCode(), UsersErrorCode.NAME_EMPTY_OR_TOO_LONG.getMsg());
        }
        if (StringUtils.hasText(email) && !EMAIL_PATTERN.matcher(email).matches()) {
            throw new BusinessException(UsersErrorCode.EMAIL_FORMAT_ERROR.getCode(), UsersErrorCode.EMAIL_FORMAT_ERROR.getMsg());
        }
        if (StringUtils.hasText(phone) && !ValidationTaiWanPhoneUtil.isValidTaiwanPhone(phone)) {
            throw new BusinessException(UsersErrorCode.PHONE_FORMAT_ERROR.getCode(), UsersErrorCode.PHONE_FORMAT_ERROR.getMsg());
        }
    }

    /**
     * 验证业务规则
     */
    private void validateBusinessRules(String employeeCode, String email, String phone, Long excludeId) {
        if (StringUtils.hasText(employeeCode) && !isEmployeeCodeUnique(employeeCode, excludeId)) {
            throw new BusinessException(UsersErrorCode.EMPLOYEE_CODE_EXIST.getCode(), UsersErrorCode.EMPLOYEE_CODE_EXIST.getMsg());
        }
        if (StringUtils.hasText(email) && !isEmailUnique(email, excludeId)) {
            throw new BusinessException(UsersErrorCode.EMAIL_ALREADY_REGISTERED.getCode(), UsersErrorCode.EMAIL_ALREADY_REGISTERED.getMsg());
        }
        if (StringUtils.hasText(phone) && !isPhoneUnique(phone, excludeId)) {
            throw new BusinessException(UsersErrorCode.PHONE_ALREADY_REGISTERED.getCode(), UsersErrorCode.PHONE_ALREADY_REGISTERED.getMsg());
        }
    }

    /**
     * 验证角色
     */
    private void validateRoles(List<Long> roleIds) {
        // 1. 基本角色存在性和状态校验
        for (Long roleId : roleIds) {
            Optional<Role> roleOpt = roleRepository.findById(roleId);
            if (roleOpt.isEmpty()) {
                throw new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), UsersErrorCode.ROLE_NOT_EXIST.getMsg()+ ":" + roleId);
            }
            Role role = roleOpt.get();
            if (role.getStatus() != 1) { // 只有有效状态的角色才能被分配
                throw new BusinessException(UsersErrorCode.ROLE_DISABLED.getCode(), UsersErrorCode.ROLE_DISABLED.getMsg() + ": " + role.getRoleName());
            }
        }

        // 2. 校验当前用户是否有权限分配这些角色
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if (userContext != null && userContext.getBusinessAccountId() != null) {
            // 获取当前用户的角色
            List<Role> currentUserRoles = roleDomainService.findByBusinessAccountId(userContext.getBusinessAccountId());
            List<Long> currentUserRoleIds = currentUserRoles.stream()
                    .map(Role::getId)
                    .collect(Collectors.toList());

            // 校验是否有权限分配指定的角色
            if (!roleDomainService.validateRoleCreationPermission(currentUserRoleIds, roleIds, userContext.getOrgId())) {
                throw new BusinessException(ResponseCode.ERROR_1.getCode(), "没有权限分配指定的角色");
            }
        }
    }

    /**
     * 关联角色
     */
    private void associateRoles(Long businessAccountId, List<Long> roleIds, String createdBy) {
        List<BusinessAccountRole> accountRoles = roleIds.stream()
                .map(roleId -> {
                    BusinessAccountRole accountRole = new BusinessAccountRole();
                    accountRole.setBusinessAccountId(businessAccountId);
                    accountRole.setRoleId(roleId);
                    accountRole.setCreatedAt(LocalDateTime.now());
                    accountRole.setUpdatedAt(LocalDateTime.now());
                    accountRole.setCreatedBy(createdBy);
                    accountRole.setUpdatedBy(createdBy);
                    accountRole.setActive(true);
                    return accountRole;
                })
                .collect(Collectors.toList());

        businessAccountRoleRepository.batchSave(accountRoles);
    }

    /**
     * 更新账号角色关联
     */
    private void updateAccountRoles(Long businessAccountId, List<Long> roleIds, String updatedBy) {
        // 删除现有关联
        businessAccountRoleRepository.deleteByBusinessAccountId(businessAccountId);

        // 创建新关联
        if (!roleIds.isEmpty()) {
            associateRoles(businessAccountId, roleIds, updatedBy);
        }
    }

    /**
     * 记录创建日志
     */
    private void recordCreateLog(Long userId, String name, String employeeCode, String email, String phone,
                                List<Long> roleIds, String createdBy) {
        try {
            Map<String, Object> newValues = new HashMap<>();
            newValues.put("name", name);
            newValues.put("employee_code", employeeCode);
            newValues.put("email", email);
            newValues.put("phone", phone);
            newValues.put("role_ids", roleIds.toString());
            newValues.put("account_type", AccountType.ADMIN.getCode());
            newValues.put("account_status", AccountStatus.ACTIVE.getCode());

            userChangeLogsService.createUserChangeLogs(userId, null, newValues, null, "ADMIN_CREATE");
        } catch (Exception e) {
            log.error("记录创建日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录修改日志
     */
    private void recordUpdateLog(Long userId, Map<String, Object> oldValues, Map<String, Object> newValues, String updatedBy) {
        try {
            userChangeLogsService.createUserChangeLogs(userId, oldValues, newValues, null, "ADMIN_UPDATE");
        } catch (Exception e) {
            log.error("记录修改日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 构建旧值
     */
    private Map<String, Object> buildOldValues(UserPerson userPerson, BusinessAccount businessAccount) {
        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("email", userPerson.getEmail());
        oldValues.put("phone", userPerson.getPhone());
        oldValues.put("account_status", businessAccount.getAccountStatus());
        oldValues.put("name", businessAccount.getAccountName());
        return oldValues;
    }

    /**
     * 构建新值
     */
    private Map<String, Object> buildNewValues(UserPerson userPerson, BusinessAccount businessAccount,
                                              String name, List<Long> roleIds) {
        Map<String, Object> newValues = new HashMap<>();
        newValues.put("email", userPerson.getEmail());
        newValues.put("phone", userPerson.getPhone());
        newValues.put("account_status", businessAccount.getAccountStatus());
        if (StringUtils.hasText(name)) {
            newValues.put("name", name);
        }
        if (roleIds != null) {
            newValues.put("role_ids", roleIds.toString());
        }
        return newValues;
    }


    private void validateEmailOrPhoneExist(String email, String phone,Long excludeId) {
        // 查询 user_person 表
        // 校验手机号是否存在
        if (StringUtils.hasText(phone)) {
            userPersonRepository.findByPhoneAndSource(phone, PlatformType.ADMIN.getCode())
                    .ifPresent(userPerson -> {
                        if (excludeId == null || !Objects.equals(userPerson.getId(), excludeId)) {
                            throw new BusinessException(
                                    UsersErrorCode.PHONE_ALREADY_REGISTERED.getCode(),
                                    UsersErrorCode.PHONE_ALREADY_REGISTERED.getMsg()
                            );
                        }
                    });
        }

        // 校验邮箱是否存在
        if (StringUtils.hasText(email)) {
            userPersonRepository.findByEmailAndSource(email, PlatformType.ADMIN.getCode())
                    .ifPresent(userPerson -> {
                        if (excludeId == null || !Objects.equals(userPerson.getId(), excludeId)) {
                            throw new BusinessException(
                                    UsersErrorCode.EMAIL_ALREADY_REGISTERED.getCode(),
                                    UsersErrorCode.EMAIL_ALREADY_REGISTERED.getMsg()
                            );
                        }
                    });
        }
    }
}
