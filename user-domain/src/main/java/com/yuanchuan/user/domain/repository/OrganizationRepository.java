package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.Organization;

import java.util.List;
import java.util.Optional;

/**
 * 组织仓储接口
 * <AUTHOR>
 * @date 2025-06-05 20:01:20:01
 */
public interface OrganizationRepository {

    /**
     * 保存组织信息
     *
     * @param organization 组织信息
     * @return 保存后的组织信息
     */
    Organization save(Organization organization);

    /**
     * 根据ID查询组织
     *
     * @param id 组织ID
     * @return 组织信息
     */
    Optional<Organization> findById(Long id);

    /**
     * 根据组织编码查询组织
     *
     * @param orgCode 组织编码
     * @return 组织信息
     */
    Optional<Organization> findByOrgCode(String orgCode);

    /**
     * 根据组织名称查询组织
     *
     * @param orgName 组织名称
     * @return 组织信息
     */
    Optional<Organization> findByOrgName(String orgName);

    /**
     * 查询所有组织
     *
     * @return 组织列表
     */
    List<Organization> findAll();

    /**
     * 根据组织类型查询组织
     *
     * @param orgType 组织类型
     * @return 组织列表
     */
    List<Organization> findByOrgType(String orgType);

    /**
     * 根据父组织ID查询子组织
     *
     * @param parentId 父组织ID
     * @return 组织列表
     */
    List<Organization> findByParentId(Long parentId);
}
