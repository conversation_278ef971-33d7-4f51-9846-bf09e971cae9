package com.yuanchuan.user.domain.service;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.BusinessAccount;

import java.util.List;

/**
 * 运营账号管理领域服务接口
 */
public interface OperationAccountDomainService {

    /**
     * 查询运营账号列表
     *
     * @param roleIds 角色ID列表，多个用逗号分隔
     * @param accountStatus 账户状态
     * @param keyword 关键词搜索
     * @param pageQuery 分页参数
     * @return 账号列表
     */
    List<BusinessAccount> queryOperationAccounts(List<Long> roleIds, Integer accountStatus, String keyword, PageQueryV pageQuery);

    /**
     * 统计运营账号数量
     *
     * @param roleIds 角色ID列表，多个用逗号分隔
     * @param accountStatus 账户状态
     * @param keyword 关键词搜索
     * @return 总数量
     */
    Long countOperationAccounts(List<Long> roleIds, Integer accountStatus, String keyword);

    /**
     * 创建运营账号
     *
     * @param name 姓名
     * @param employeeCode 员工编码
     * @param email 邮箱
     * @param phone 手机号
     * @param roleIds 角色ID列表
     * @param createdBy 创建人
     * @return 账号ID
     */
    Long createOperationAccount(String name, String employeeCode, String email, String phone,
                               List<Long> roleIds, String createdBy);

    /**
     * 修改运营账号
     *
     * @param id 账号ID
     * @param name 姓名
     * @param email 邮箱
     * @param phone 手机号
     * @param roleIds 角色ID列表
     * @param updatedBy 更新人
     * @return 是否成功
     */
    Boolean updateOperationAccount(Long id, String name, String email, String phone, 
                                  List<Long> roleIds,Integer status, String updatedBy);

    /**
     * 根据ID查询运营账号
     *
     * @param id 账号ID
     * @return 账号信息
     */
    BusinessAccount getOperationAccountById(Long id);

    /**
     * 验证员工编码是否唯一
     *
     * @param employeeCode 员工编码
     * @param excludeId 排除的账号ID（用于修改时排除自己）
     * @return 是否唯一
     */
    Boolean isEmployeeCodeUnique(String employeeCode, Long excludeId);

    /**
     * 验证邮箱是否唯一
     *
     * @param email 邮箱
     * @param excludeId 排除的账号ID（用于修改时排除自己）
     * @return 是否唯一
     */
    Boolean isEmailUnique(String email, Long excludeId);

    /**
     * 验证手机号是否唯一
     *
     * @param phone 手机号
     * @param excludeId 排除的账号ID（用于修改时排除自己）
     * @return 是否唯一
     */
    Boolean isPhoneUnique(String phone, Long excludeId);
}
