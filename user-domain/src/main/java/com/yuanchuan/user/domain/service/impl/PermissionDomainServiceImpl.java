package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.domain.model.Permission;
import com.yuanchuan.user.domain.model.RolePermission;
import com.yuanchuan.user.domain.repository.PermissionRepository;
import com.yuanchuan.user.domain.repository.RolePermissionRepository;
import com.yuanchuan.user.domain.service.PermissionDomainService;
import com.yuanchuan.user.domain.service.AuditLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 权限领域服务实现类
 */
@Slf4j
@Service
public class PermissionDomainServiceImpl implements PermissionDomainService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private AuditLogDomainService auditLogDomainService;

    @Autowired
    private RolePermissionRepository rolePermissionRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Permission createPermission(Permission permission,Long createdId,String createdBy) {
        // 校验权限名称唯一性
        if (!checkPermissionNameUnique(permission.getPermissionName(), null)) {
            throw new BusinessException(UsersErrorCode.PERMISSION_NAME_EXIST.getCode(), UsersErrorCode.PERMISSION_NAME_EXIST.getMsg());
        }

        // 校验权限编码唯一性
        if (!checkPermissionCodeUnique(permission.getPermissionCode(), null)) {
            throw new BusinessException(UsersErrorCode.PERMISSION_CODE_EXIST.getCode(), UsersErrorCode.PERMISSION_CODE_EXIST.getMsg());
        }

        // 设置默认值
        permission.setStatus(1);
        permission.setActive(1);
        permission.setCreatedAt(new Date());
        permission.setUpdatedAt(new Date());
        permission.setCreatedBy(createdBy);
        permission.setUpdatedBy(createdBy);

        Permission savedPermission = permissionRepository.save(permission);

        // 记录创建日志
        Map<String, Object> newValues = new HashMap<>();
        newValues.put("permission_name", savedPermission.getPermissionName());
        newValues.put("permission_code", savedPermission.getPermissionCode());
        newValues.put("type", savedPermission.getType());
        newValues.put("status", savedPermission.getStatus());

        auditLogDomainService.recordAuditLog(
                "PERMISSION",
                savedPermission.getId(),
                "CREATE",
                createdId,
                createdBy,
                null,
                newValues,
                "创建权限"
        );

        return savedPermission;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Permission updatePermission(Permission permission,Long createdId,String createdBy) {
        // 校验权限是否存在
        Permission existingPermission = getPermissionById(permission.getId());

        // 记录变更前的数据
        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("permission_name", existingPermission.getPermissionName());
        oldValues.put("permission_code", existingPermission.getPermissionCode());
        oldValues.put("description", existingPermission.getDescription());
        oldValues.put("status", existingPermission.getStatus());

        // 校验权限名称唯一性
        if (!checkPermissionNameUnique(permission.getPermissionName(), existingPermission.getId())) {
            throw new BusinessException(UsersErrorCode.PERMISSION_NAME_EXIST.getCode(), UsersErrorCode.PERMISSION_NAME_EXIST.getMsg());
        }

        // 校验权限编码唯一性
        if (!checkPermissionCodeUnique(permission.getPermissionCode(), existingPermission.getId())) {
            throw new BusinessException(UsersErrorCode.PERMISSION_CODE_EXIST.getCode(), UsersErrorCode.PERMISSION_CODE_EXIST.getMsg());
        }

        // 更新字段
        existingPermission.setPermissionName(permission.getPermissionName());
        existingPermission.setPermissionCode(permission.getPermissionCode());
        existingPermission.setParentId(permission.getParentId());
        existingPermission.setDescription(permission.getDescription());
        if (permission.getStatus() != null) {
            existingPermission.setStatus(permission.getStatus());
        }
        existingPermission.setUpdatedAt(new Date());
        existingPermission.setUpdatedBy(createdBy);

        Permission savedPermission = permissionRepository.save(existingPermission);

        // 记录变更日志
        Map<String, Object> newValues = new HashMap<>();
        newValues.put("permission_name", savedPermission.getPermissionName());
        newValues.put("permission_code", savedPermission.getPermissionCode());
        newValues.put("description", savedPermission.getDescription());
        newValues.put("status", savedPermission.getStatus());

        auditLogDomainService.recordAuditLog(
                "PERMISSION",
                savedPermission.getId(),
                "UPDATE",
                createdId,
                createdBy,
                oldValues,
                newValues,
                "更新权限信息"
        );

        return savedPermission;
    }

    @Override
    public Permission getPermissionById(Long id) {
        Permission permission = permissionRepository.findByPermissionId(id);
        if (Objects.isNull(permission)) {
            throw new BusinessException(UsersErrorCode.PERMISSION_NOT_EXIST.getCode(), UsersErrorCode.PERMISSION_NOT_EXIST.getMsg());
        }
        return permission;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePermission(Long id) {
        // 校验权限是否存在
        getPermissionById(id);

        // 校验是否有角色关联此权限，如果有则不允许删除
        List<RolePermission> rolePermissionList = rolePermissionRepository.findByPermissionId(id);
        if(!CollectionUtils.isEmpty(rolePermissionList)){
            throw new BusinessException(UsersErrorCode.ROLE_RELATED_PERMISSION_NOT_ALLOWED_TO_DELETE.getCode(), UsersErrorCode.ROLE_RELATED_PERMISSION_NOT_ALLOWED_TO_DELETE.getMsg());
        }

        return permissionRepository.deleteById(id);
    }

    @Override
    public List<Permission> getUserPermissionTree(Long accountId) {
        return permissionRepository.findByAccountId(accountId);
    }

    @Override
    public List<Permission> queryPermissions(String permissionMenu, Integer status, String keyword, PageQueryV pageQuery) {
        return permissionRepository.findByConditions(permissionMenu, status, keyword, pageQuery);
    }

    @Override
    public Long countPermissions(String permissionMenu, Integer status, String keyword) {
        return permissionRepository.countByConditions(permissionMenu, status, keyword);
    }

    @Override
    public List<Permission> getPermissionEntries() {
        return permissionRepository.findByType("MENU");
    }

    @Override
    public Boolean checkPermissionNameUnique(String permissionName, Long excludeId) {
        if (!StringUtils.hasText(permissionName)) {
            return true;
        }

        Optional<Permission> permission = permissionRepository.findByPermissionName(permissionName);
        if (!permission.isPresent()) {
            return true;
        }

        return excludeId != null && permission.get().getId().equals(excludeId);
    }

    @Override
    public Boolean checkPermissionCodeUnique(String permissionCode, Long excludeId) {
        if (!StringUtils.hasText(permissionCode)) {
            return true;
        }

        Optional<Permission> permission = permissionRepository.findByPermissionCode(permissionCode);
        if (!permission.isPresent()) {
            return true;
        }

        return excludeId != null && permission.get().getId().equals(excludeId);
    }
}
