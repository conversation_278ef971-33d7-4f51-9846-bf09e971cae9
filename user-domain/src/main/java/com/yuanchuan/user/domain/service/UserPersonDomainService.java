package com.yuanchuan.user.domain.service;


import com.yuanchuan.user.domain.model.*;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface UserPersonDomainService {
    /**
     * 登录/注册
     *
     * @param userRegisterDomain 请求
     * @return 登录响应
     */
    UserPerson verifySmsCodeAndLogin(UserRegisterDomain userRegisterDomain);

    /**
     * 验证邮箱验证码并登录/注册
     *
     * @param userRegisterDomain 验证请求
     * @return 用户领域对象
     */
    UserPerson verifyEmailCodeAndLogin(UserRegisterDomain userRegisterDomain);

    /**
     * 用户注册
     *
     * @param userRegisterDomain 注册请求
     * @return 用户领域对象
     */
    UserPerson register(UserRegisterDomain userRegisterDomain);

    /**
     * 用户密码验证
     *
     * @param userRegisterDomain 密码验证请求
     * @return 用户领域对象
     */
    UserPerson verifyPassword(UserRegisterDomain userRegisterDomain);

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户领域对象
     */
    UserPerson findById(Long userId);

    /**
     * 根据手机号获取用户信息
     *
     * @param phone 手机号
     * @param source 来源
     * @return 用户领域对象
     */
    UserPerson findByPhoneAndSource(String phone, String source);

    /**
     * 检查手机号是否已注册
     *
     * @param phone 手机号
     * @param source 来源
     * @return 是否已注册
     */
    boolean checkPhoneExists(String phone, String source);

    /**
     * 根据邮箱获取用户信息
     *
     * @param email 邮箱
     * @param source 来源
     * @return 用户领域对象
     */
    UserPerson findByEmailAndSource(String email, String source);

    /**
     * 检查邮箱是否已注册
     *
     * @param email 邮箱
     * @param source 来源
     * @return 是否已注册
     */
    boolean checkEmailExists(String email, String source);

    /**
     * 更新用户信息
     *
     * @param userId             用户ID
     * @param userRegisterDomain 更新请求
     * @return 更新后的用户领域对象
     */
    UserPerson updateUser(Long userId, UserRegisterDomain userRegisterDomain);


    /**
     * 确认使用已有账号
     *
     * @param userRegisterDomain 确认请求
     * @return 用户领域对象
     */
    UserPerson confirmUseExistingAccount(UserRegisterDomain userRegisterDomain);

    /**
     * 创建新账号（邮箱+手机号）
     *
     * @param userRegisterDomain 创建请求
     * @return 用户领域对象
     */
    UserPerson createNewAccountWithEmail(UserRegisterDomain userRegisterDomain);

    /**
     * 更新用户昵称
     *
     * @param userId   用户ID
     * @param nickname 昵称
     * @return 更新后的用户领域对象
     */
    void updateNickname(Long userId, String nickname);

    /**
     * 更新用户头像
     *
     * @param userId    用户ID
     * @param avatarUrl 头像URL
     * @return 更新后的用户领域对象
     */
    void updateAvatar(Long userId, String avatarUrl);

    /**
     * 更新用户性别
     *
     * @param userId 用户ID
     * @param gender 性别
     * @return 更新后的用户领域对象
     */
    void updateGender(Long userId, String gender);

    /**
     * 更新用户生日
     *
     * @param userId   用户ID
     * @param birthday 生日
     * @return 更新后的用户领域对象
     */
    void updateBirthday(Long userId, LocalDate birthday);

    /**
     * 更新用户常居地
     *
     * @param userId       用户ID
     * @param address      常居地
     * @param provinceCode 省编码
     * @param cityCode     市编码
     * @param regionCode   区编码
     * @param provinceName 省名称
     * @param cityName     市名称
     * @param regionName   区名称
     * @param longitude    经度
     * @param latitude     纬度
     * @return 更新后的用户领域对象
     */
    void updateAddress(Long userId, String address, String provinceCode, String cityCode, String regionCode,
                       String provinceName, String cityName, String regionName,
                       BigDecimal longitude, BigDecimal latitude);

    /**
     * 验证手机号是否是当前账号绑定的手机号
     *
     * @param userId 用户ID
     * @param phone  手机号
     * @return 是否是当前账号绑定的手机号
     */
    boolean verifyPhone(Long userId, String phone);

    /**
     * 换绑手机号
     *
     * @param userId   用户ID
     * @param newPhone 新手机号
     * @param deviceId 设备ID
     * @return 是否换绑成功
     */
    boolean updatePhoneAndSource(Long userId, String newPhone, String deviceId,String source);

    /**
     * 绑定邮箱
     *
     * @param userId 用户ID
     * @param email  邮箱
     * @return 是否绑定成功
     */
    boolean bindEmailAndSource(Long userId, String email,String source);

    /**
     * 验证原邮箱
     *
     * @param userId 用户ID
     * @param email  原邮箱
     * @return 是否验证成功
     */
    boolean verifyOriginalEmail(Long userId, String email);

    /**
     * 设置密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 是否设置成功
     */
    boolean setPassword(Long userId, String password);

    /**
     * 验证密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 是否验证成功
     */
    boolean verifyPassword(Long userId, String password);

    /**
     * 修改密码
     *
     * @param userId      用户ID
     * @param oldPassword 原密码
     * @return 是否修改成功
     */
    boolean verifyOriginalPassword(Long userId, String oldPassword);

    /**
     * 检查设备换绑次数限制
     *
     * @param deviceId 设备ID
     * @return 是否超过限制
     */
    boolean checkDevicePhoneUpdateLimit(String deviceId);

    /**
     * 创建商户账户
     *
     * @param userRegisterDomain 注册请求
     * @return 商户账户领域对象
     */
    UserPerson createBusinessAccount(UserRegisterDomain userRegisterDomain);

    /**
     * 根据自然人ID查询商户账户
     *
     * @param userPersonId 自然人ID
     * @return 商户账户领域对象
     */
    BusinessAccount findBusinessAccountByUserPersonId(Long userPersonId);

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色领域对象
     */
    Role findRoleByRoleCode(String roleCode);


    /**
     * 验证新邮箱
     *
     * @param userId 用户ID
     * @param email  原邮箱
     * @return 是否验证成功
     */
    boolean verifyNewEmail(Long userId,String platform, String email);

    /**
     * 绑定设备
     *
     * @param accountDevice 设备信息
     * @return 是否绑定成功
     */
    boolean bindDevice(AccountDevice accountDevice);

    /**
     * 更新客户账户信息
     *
     * @param customerAccount 客户账户信息
     * @return 是否更新成功
     */
    boolean updateCustomerAccount(CustomerAccount customerAccount);
}
