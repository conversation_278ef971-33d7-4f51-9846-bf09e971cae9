package com.yuanchuan.user.domain.service;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.Role;

import java.util.List;

/**
 * 角色领域服务接口
 */
public interface RoleDomainService {

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @param permissionIds 权限ID列表
     * @return 创建后的角色信息
     */
    Role createRole(Role role, List<Long> permissionIds,String createBy);

    /**
     * 更新角色
     *
     * @param role 角色信息
     * @param permissionIds 权限ID列表
     * @return 更新后的角色信息
     */
    Role updateRole(Role role, List<Long> permissionIds,Long createId, String createBy);

    /**
     * 根据ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    Role getRoleById(Long id);

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @param orgId 组织ID
     * @return 是否成功
     */
    Boolean deleteRole(Long id, Long orgId, String createBy);

    /**
     * 根据账户ID查询用户角色
     *
     * @param accountId 账户ID
     * @param orgId 组织ID
     * @return 角色列表
     */
    List<Role> getUserRoles(Long accountId, Long orgId);

    /**
     * 根据商户账户ID查询角色列表
     *
     * @param businessAccountId 商户账户ID
     * @return 角色列表
     */
    List<Role> findByBusinessAccountId(Long businessAccountId);

    /**
     * 分页查询角色
     *
     * @param roleId 角色ID
     * @param status 状态
     * @param keyword 关键词
     * @param pageQuery 分页查询参数
     * @return 角色列表
     */
    List<Role> queryRoles(String roleId, Integer status, String keyword,Long orgId, PageQueryV pageQuery);

    /**
     * 统计角色数量
     *
     * @param roleName 角色名称
     * @param status 状态
     * @param keyword 关键词
     * @param orgId 组织ID
     * @return 角色数量
     */
    Long countRoles(String roleName, Integer status, String keyword, Long orgId);

    /**
     * 获取角色下拉列表
     *
     * @param orgId 组织ID
     * @return 角色列表
     */
    List<Role> getRoleSelectList(Long orgId);

    /**
     * 校验角色名称唯一性
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否唯一
     */
    Boolean checkRoleNameUnique(String roleName, Long excludeId);


    /**
     * 校验角色名称唯一性
     *
     * @param roleCode 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否唯一
     */
    Boolean checkRoleCodeUnique(String roleCode, Long excludeId);

    /**
     * 根据角色ID查询权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getPermissionIdsByRoleId(Long roleId);

    Role queryRoleById(Long id, Long orgId);

    /**
     * 商户创建角色
     *
     * @param merchantId 商户ID
     * @param merchantName 商户名称
     * @param orgType 组织类型
     * @param businessAccountId 业务账户ID
     * @return 是否成功
     */
    Boolean merchantCreateRole(Long merchantId, String merchantName, String orgType, Long businessAccountId);

    /**
     * 获取当前用户角色允许创建的目标角色ID列表
     *
     * @param currentUserRoleIds 当前用户的角色ID列表
     * @param orgId 组织ID
     * @return 允许创建的目标角色ID列表
     */
    List<Long> getAllowedTargetRoleIds(List<Long> currentUserRoleIds, Long orgId);

    /**
     * 校验当前用户是否有权限创建指定角色
     *
     * @param currentUserRoleIds 当前用户的角色ID列表
     * @param targetRoleIds 要创建的目标角色ID列表
     * @param orgId 组织ID
     * @return 是否有权限
     */
    Boolean validateRoleCreationPermission(List<Long> currentUserRoleIds, List<Long> targetRoleIds, Long orgId);

    /**
     * 保存角色创建权限配置
     *
     * @param roleId 角色ID
     * @param targetRoleIds 目标角色ID列表
     * @param orgId 组织ID
     * @param createdBy 创建人
     */
    void saveRoleCreateScopes(Long roleId, List<Long> targetRoleIds, Long orgId, String createdBy);

    /**
     * 根据角色ID获取其允许创建的目标角色ID列表
     *
     * @param roleId 角色ID
     * @param orgId 组织ID
     * @return 目标角色ID列表
     */
    List<Long> getTargetRoleIdsByRoleId(Long roleId, Long orgId);
}
