package com.yuanchuan.user.domain.model;

import java.util.Date;

/**
 * 角色创建配置表
 * @TableName role_create_scope
 */
public class RoleCreateScope {
    /**
     * 
     */
    private Long id;

    /**
     * 允许创建角色的角色ID（当前用户拥有角色）
     */
    private Long fromRoleId;

    /**
     * 被创建的目标角色ID（系统允许其创建角色）
     */
    private Long toRoleId;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 
     */
    private Integer active;

    /**
     * 
     */
    private Date createdAt;

    /**
     * 
     */
    private Date updatedAt;

    /**
     * 
     */
    public Long getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 允许创建角色的角色ID（当前用户拥有角色）
     */
    public Long getFromRoleId() {
        return fromRoleId;
    }

    /**
     * 允许创建角色的角色ID（当前用户拥有角色）
     */
    public void setFromRoleId(Long fromRoleId) {
        this.fromRoleId = fromRoleId;
    }

    /**
     * 被创建的目标角色ID（系统允许其创建角色）
     */
    public Long getToRoleId() {
        return toRoleId;
    }

    /**
     * 被创建的目标角色ID（系统允许其创建角色）
     */
    public void setToRoleId(Long toRoleId) {
        this.toRoleId = toRoleId;
    }

    /**
     * 组织id
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     * 组织id
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 
     */
    public Integer getActive() {
        return active;
    }

    /**
     * 
     */
    public void setActive(Integer active) {
        this.active = active;
    }

    /**
     * 
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * 
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * 
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RoleCreateScope other = (RoleCreateScope) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFromRoleId() == null ? other.getFromRoleId() == null : this.getFromRoleId().equals(other.getFromRoleId()))
            && (this.getToRoleId() == null ? other.getToRoleId() == null : this.getToRoleId().equals(other.getToRoleId()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getActive() == null ? other.getActive() == null : this.getActive().equals(other.getActive()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFromRoleId() == null) ? 0 : getFromRoleId().hashCode());
        result = prime * result + ((getToRoleId() == null) ? 0 : getToRoleId().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getActive() == null) ? 0 : getActive().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", fromRoleId=").append(fromRoleId);
        sb.append(", toRoleId=").append(toRoleId);
        sb.append(", orgId=").append(orgId);
        sb.append(", active=").append(active);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }
}