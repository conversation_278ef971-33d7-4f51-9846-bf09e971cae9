package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 角色创建权限配置领域对象
 */
@Data
@Accessors(chain = true)
public class RoleCreateScope {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 源角色ID（拥有创建权限的角色）
     */
    private Long fromRoleId;

    /**
     * 目标角色ID（允许被创建的角色）
     */
    private Long toRoleId;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 是否启用 0-禁用 1-启用
     */
    private Integer active;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;
}