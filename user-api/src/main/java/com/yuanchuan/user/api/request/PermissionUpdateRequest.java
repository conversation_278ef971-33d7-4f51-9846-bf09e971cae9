package com.yuanchuan.user.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * 权限更新请求
 */
@Data
@Schema(description = "权限更新请求")
public class PermissionUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "權限ID不能為空")
    @Schema(description = "权限ID", required = true)
    private Long id;

    @Size(max = 40, message = "權限名稱不能超過 40 個字元")
    @Schema(description = "权限名称", required = true)
    private String permissionName;

    @Size(max = 200, message = "權限說明不能超過 200 個字元")
    @Schema(description = "权限说明")
    private String description;

    @Size(max = 200, message = "權限Key值不能超過 200 個字元")
    @Schema(description = "权限Key值", required = true)
    private String permissionCode;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "父权限ID")
    private Long parentId;


}
