package com.yuanchuan.user.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 运营账号修改请求
 */
@Data
@Schema(description = "运营账号修改请求")
public class OperationAccountUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "帳號ID不能為空")
    @Schema(description = "账号ID", example = "1", required = true)
    private Long id;

    @Size(max = 20, message = "姓名不能超過20個字元")
    @Schema(description = "姓名", example = "张三")
    private String name;

    @Email(message = "郵箱格式不正確")
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Pattern(regexp = "^09\\d{8}$", message = "請輸入正確的台灣手機號碼")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********")
    private String phone;

    @Schema(description = "角色ID列表，支持多选", example = "[1, 2, 3]")
    private List<Long> roleIds;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;
}
