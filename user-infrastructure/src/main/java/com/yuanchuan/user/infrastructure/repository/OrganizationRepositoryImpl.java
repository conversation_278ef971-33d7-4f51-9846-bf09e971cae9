package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.Organization;
import com.yuanchuan.user.domain.repository.OrganizationRepository;
import com.yuanchuan.user.infrastructure.mapper.OrganizationMapper;
import com.yuanchuan.user.infrastructure.po.OrganizationPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 组织仓储实现类
 * <AUTHOR>
 * @date 2025-06-05 20:02:20:02
 */
@Slf4j
@Repository
public class OrganizationRepositoryImpl implements OrganizationRepository {

    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    public Organization save(Organization organization) {
        OrganizationPO po = toPO(organization);
        if (po.getId() == null) {
            organizationMapper.insert(po);
        } else {
            organizationMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<Organization> findById(Long id) {
        LambdaQueryWrapper<OrganizationPO> query = new LambdaQueryWrapper<>();
        query.eq(OrganizationPO::getId, id)
                .eq(OrganizationPO::getActive, 1);
        OrganizationPO po = organizationMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<Organization> findByOrgCode(String orgCode) {
        LambdaQueryWrapper<OrganizationPO> query = new LambdaQueryWrapper<>();
        query.eq(OrganizationPO::getOrgCode, orgCode)
                .eq(OrganizationPO::getActive, 1);
        OrganizationPO po = organizationMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<Organization> findByOrgName(String orgName) {
        LambdaQueryWrapper<OrganizationPO> query = new LambdaQueryWrapper<>();
        query.eq(OrganizationPO::getOrgName, orgName)
                .eq(OrganizationPO::getActive, 1);
        OrganizationPO po = organizationMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<Organization> findAll() {
        LambdaQueryWrapper<OrganizationPO> query = new LambdaQueryWrapper<>();
        query.eq(OrganizationPO::getActive, 1)
                .orderByAsc(OrganizationPO::getSortOrder);
        List<OrganizationPO> poList = organizationMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Organization> findByOrgType(String orgType) {
        LambdaQueryWrapper<OrganizationPO> query = new LambdaQueryWrapper<>();
        query.eq(OrganizationPO::getOrgType, orgType)
                .eq(OrganizationPO::getActive, 1)
                .orderByAsc(OrganizationPO::getSortOrder);
        List<OrganizationPO> poList = organizationMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Organization> findByParentId(Long parentId) {
        LambdaQueryWrapper<OrganizationPO> query = new LambdaQueryWrapper<>();
        query.eq(OrganizationPO::getParentId, parentId)
                .eq(OrganizationPO::getActive, 1)
                .orderByAsc(OrganizationPO::getSortOrder);
        List<OrganizationPO> poList = organizationMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    /**
     * PO转Entity
     */
    private Organization toEntity(OrganizationPO po) {
        if (po == null) {
            return null;
        }
        Organization entity = new Organization();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }

    /**
     * Entity转PO
     */
    private OrganizationPO toPO(Organization entity) {
        if (entity == null) {
            return null;
        }
        OrganizationPO po = new OrganizationPO();
        BeanUtils.copyProperties(entity, po);
        return po;
    }
}
