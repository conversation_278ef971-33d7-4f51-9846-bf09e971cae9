package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.RoleCreateScope;
import com.yuanchuan.user.domain.repository.RoleCreateScopeRepository;
import com.yuanchuan.user.infrastructure.mapper.RoleCreateScopeMapper;
import com.yuanchuan.user.infrastructure.po.RoleCreateScopePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色创建权限配置仓储实现类
 */
@Slf4j
@Repository
public class RoleCreateScopeRepositoryImpl implements RoleCreateScopeRepository {

    @Autowired
    private RoleCreateScopeMapper roleCreateScopeMapper;

    @Override
    public RoleCreateScope save(RoleCreateScope roleCreateScope) {
        RoleCreateScopePO po = toPO(roleCreateScope);
        if (po.getId() == null) {
            roleCreateScopeMapper.insert(po);
        } else {
            roleCreateScopeMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<RoleCreateScope> findById(Long id) {
        LambdaQueryWrapper<RoleCreateScopePO> query = new LambdaQueryWrapper<>();
        query.eq(RoleCreateScopePO::getId, id)
                .eq(RoleCreateScopePO::getActive, 1);
        RoleCreateScopePO po = roleCreateScopeMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<Long> findTargetRoleIdsByFromRoleId(Long fromRoleId, Long orgId) {
        LambdaQueryWrapper<RoleCreateScopePO> query = new LambdaQueryWrapper<>();
        query.eq(RoleCreateScopePO::getFromRoleId, fromRoleId)
                .eq(RoleCreateScopePO::getOrgId, orgId)
                .eq(RoleCreateScopePO::getActive, 1)
                .select(RoleCreateScopePO::getToRoleId);
        
        List<RoleCreateScopePO> poList = roleCreateScopeMapper.selectList(query);
        return poList.stream()
                .map(RoleCreateScopePO::getToRoleId)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<RoleCreateScope> findByFromRoleIdAndToRoleId(Long fromRoleId, Long toRoleId, Long orgId) {
        LambdaQueryWrapper<RoleCreateScopePO> query = new LambdaQueryWrapper<>();
        query.eq(RoleCreateScopePO::getFromRoleId, fromRoleId)
                .eq(RoleCreateScopePO::getToRoleId, toRoleId)
                .eq(RoleCreateScopePO::getOrgId, orgId)
                .eq(RoleCreateScopePO::getActive, 1);
        
        RoleCreateScopePO po = roleCreateScopeMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<RoleCreateScope> findByFromRoleId(Long fromRoleId, Long orgId) {
        LambdaQueryWrapper<RoleCreateScopePO> query = new LambdaQueryWrapper<>();
        query.eq(RoleCreateScopePO::getFromRoleId, fromRoleId)
                .eq(RoleCreateScopePO::getOrgId, orgId)
                .eq(RoleCreateScopePO::getActive, 1);
        
        List<RoleCreateScopePO> poList = roleCreateScopeMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<RoleCreateScope> findByToRoleId(Long toRoleId, Long orgId) {
        LambdaQueryWrapper<RoleCreateScopePO> query = new LambdaQueryWrapper<>();
        query.eq(RoleCreateScopePO::getToRoleId, toRoleId)
                .eq(RoleCreateScopePO::getOrgId, orgId)
                .eq(RoleCreateScopePO::getActive, 1);
        
        List<RoleCreateScopePO> poList = roleCreateScopeMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public boolean deleteById(Long id) {
        // 逻辑删除
        RoleCreateScopePO po = new RoleCreateScopePO();
        po.setId(id);
        po.setActive(0);
        po.setUpdatedAt(LocalDateTime.now());
        return roleCreateScopeMapper.updateById(po) > 0;
    }

    @Override
    public boolean deleteByFromRoleId(Long fromRoleId, Long orgId) {
        LambdaQueryWrapper<RoleCreateScopePO> query = new LambdaQueryWrapper<>();
        query.eq(RoleCreateScopePO::getFromRoleId, fromRoleId)
                .eq(RoleCreateScopePO::getOrgId, orgId);
        
        RoleCreateScopePO updatePO = new RoleCreateScopePO();
        updatePO.setActive(0);
        updatePO.setUpdatedAt(LocalDateTime.now());
        
        return roleCreateScopeMapper.update(updatePO, query) > 0;
    }

    @Override
    public List<RoleCreateScope> saveAll(List<RoleCreateScope> roleCreateScopes) {
        return roleCreateScopes.stream()
                .map(this::save)
                .collect(Collectors.toList());
    }

    /**
     * 将领域对象转换为持久化对象
     */
    private RoleCreateScopePO toPO(RoleCreateScope entity) {
        RoleCreateScopePO po = new RoleCreateScopePO();
        BeanUtils.copyProperties(entity, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private RoleCreateScope toEntity(RoleCreateScopePO po) {
        RoleCreateScope entity = new RoleCreateScope();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
