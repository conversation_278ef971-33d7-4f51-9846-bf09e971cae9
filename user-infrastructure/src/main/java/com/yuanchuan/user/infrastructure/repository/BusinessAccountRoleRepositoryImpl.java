package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.BusinessAccountRole;
import com.yuanchuan.user.domain.repository.BusinessAccountRoleRepository;
import com.yuanchuan.user.infrastructure.mapper.BusinessAccountRoleMapper;
import com.yuanchuan.user.infrastructure.po.BusinessAccountRolePO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商户账户角色关联仓储实现类
 */
@Repository
public class BusinessAccountRoleRepositoryImpl implements BusinessAccountRoleRepository {

    @Autowired
    private BusinessAccountRoleMapper businessAccountRoleMapper;

    @Override
    public BusinessAccountRole save(BusinessAccountRole businessAccountRole) {
        BusinessAccountRolePO po = toPO(businessAccountRole);
        if (po.getId() == null) {
            businessAccountRoleMapper.insert(po);
        } else {
            businessAccountRoleMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    @Transactional
    public List<BusinessAccountRole> batchSave(List<BusinessAccountRole> businessAccountRoles) {
        List<BusinessAccountRole> result = new ArrayList<>();
        for (BusinessAccountRole businessAccountRole : businessAccountRoles) {
            result.add(save(businessAccountRole));
        }
        return result;
    }

    @Override
    public Optional<BusinessAccountRole> findById(Long id) {
        LambdaQueryWrapper<BusinessAccountRolePO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountRolePO::getId, id)
                .eq(BusinessAccountRolePO::getActive, true);
        BusinessAccountRolePO po = businessAccountRoleMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<BusinessAccountRole> findByBusinessAccountId(Long businessAccountId) {
        LambdaQueryWrapper<BusinessAccountRolePO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountRolePO::getBusinessAccountId, businessAccountId)
                .eq(BusinessAccountRolePO::getActive, true);
        List<BusinessAccountRolePO> poList = businessAccountRoleMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<BusinessAccountRole> findByRoleId(Long roleId) {
        LambdaQueryWrapper<BusinessAccountRolePO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountRolePO::getRoleId, roleId)
                .eq(BusinessAccountRolePO::getActive, true);
        List<BusinessAccountRolePO> poList = businessAccountRoleMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public Optional<BusinessAccountRole> findByBusinessAccountIdAndRoleId(Long businessAccountId, Long roleId,Long organizationId) {
        LambdaQueryWrapper<BusinessAccountRolePO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountRolePO::getBusinessAccountId, businessAccountId)
                .eq(BusinessAccountRolePO::getRoleId, roleId)
                .eq(BusinessAccountRolePO::getOrgId, organizationId)
                .eq(BusinessAccountRolePO::getActive, true);
        BusinessAccountRolePO po = businessAccountRoleMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public int deleteByBusinessAccountId(Long businessAccountId) {
        LambdaQueryWrapper<BusinessAccountRolePO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountRolePO::getBusinessAccountId, businessAccountId);
        BusinessAccountRolePO po = new BusinessAccountRolePO();
        po.setActive(false);
        return businessAccountRoleMapper.update(po, query);
    }

    @Override
    public int deleteByBusinessAccountIdAndRoleId(Long businessAccountId, Long roleId) {
        LambdaQueryWrapper<BusinessAccountRolePO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountRolePO::getBusinessAccountId, businessAccountId)
                .eq(BusinessAccountRolePO::getRoleId, roleId);
        BusinessAccountRolePO po = new BusinessAccountRolePO();
        po.setActive(false);
        return businessAccountRoleMapper.update(po, query);
    }

    /**
     * 将领域对象转换为持久化对象
     */
    private BusinessAccountRolePO toPO(BusinessAccountRole businessAccountRole) {
        BusinessAccountRolePO po = new BusinessAccountRolePO();
        BeanUtils.copyProperties(businessAccountRole, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private BusinessAccountRole toEntity(BusinessAccountRolePO po) {
        BusinessAccountRole businessAccountRole = new BusinessAccountRole();
        BeanUtils.copyProperties(po, businessAccountRole);
        return businessAccountRole;
    }
}
