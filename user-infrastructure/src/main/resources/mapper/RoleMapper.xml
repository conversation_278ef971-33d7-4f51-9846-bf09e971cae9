<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.RolePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="roleCode" column="role_code" jdbcType="VARCHAR"/>
            <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,role_code,role_name,
        org_id,description,status,
        active,created_at,updated_at,
        created_by,updated_by
    </sql>


    <select id="findByConditions" resultType="com.yuanchuan.user.infrastructure.po.RolePO">
        SELECT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.`status`,
            r.updated_by,
            r.updated_at,
            GROUP_CONCAT(rp.permission_id ORDER BY rp.permission_id) AS permissionIds
        FROM role r
                 LEFT JOIN role_permission rp ON r.id = rp.role_id AND rp.active = 1
                 LEFT JOIN permission p ON rp.permission_id = p.id AND p.active = 1
        WHERE r.active = 1
          <if test="roleId != null and roleId !=''">
              AND r.id = #{roleId}
          </if>
          <if test="status != null and status !=''">
              AND r.status = #{status}
          </if>
          <if test="keyword != null and keyword != ''">
              AND (
              r.role_name LIKE CONCAT('%',#{keyword}, '%')
              OR p.permission_name LIKE CONCAT('%', #{keyword}, '%')
              )
          </if>

        <if test="orgId != null and orgId !=''">
            AND r.org_id = #{orgId}
        </if>

        GROUP BY r.id, r.role_name, r.status, r.updated_by, r.updated_at
        ORDER BY r.id DESC
        <if test="usePaging">
            LIMIT #{offset}, #{limit}
        </if>

    </select>


    <select id="findByConditionsCount" resultType="java.lang.Long">
        select count(1) from
        (SELECT
        r.id,
        r.role_name,
        r.`status`,
        r.updated_by,
        r.updated_at,
        GROUP_CONCAT(rp.permission_id ORDER BY rp.permission_id) AS permissionIds
        FROM role r
        LEFT JOIN role_permission rp ON r.id = rp.role_id AND rp.active = 1
        LEFT JOIN permission p ON rp.permission_id = p.id AND p.active = 1
        WHERE r.active = 1
        <if test="keyword != null and keyword != ''">
            AND (
            r.role_name LIKE CONCAT('%',#{keyword}, '%')
            OR p.permission_name LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="orgId != null and orgId !=''">
            AND r.org_id = #{orgId}
        </if>

        GROUP BY r.id, r.role_name, r.status, r.updated_by, r.updated_at
        ) ts
    </select>


    <select id="queryRoleById" resultType="com.yuanchuan.user.infrastructure.po.RolePO">
        SELECT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.status,
            r.updated_by,
            r.updated_at,
            GROUP_CONCAT(rp.permission_id ORDER BY rp.permission_id) AS permissionIds
        FROM role r
                 LEFT JOIN role_permission rp ON r.id = rp.role_id AND rp.active = 1
                 LEFT JOIN permission p ON rp.permission_id = p.id AND p.active = 1
        WHERE r.active = 1
          AND r.id = #{roleId}
        GROUP BY r.id, r.role_name, r.role_code, r.description, r.status, r.updated_by, r.updated_at

    </select>

    <select id="queryRoleByIdAndOrgId" resultType="com.yuanchuan.user.infrastructure.po.RolePO">
        SELECT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.status,
            r.updated_by,
            r.updated_at,
            GROUP_CONCAT(rp.permission_id ORDER BY rp.permission_id) AS permissionIds
        FROM role r
                 LEFT JOIN role_permission rp ON r.id = rp.role_id AND rp.active = 1
                 LEFT JOIN permission p ON rp.permission_id = p.id AND p.active = 1
        WHERE r.active = 1
          AND r.id = #{roleId}
          AND r.org_id = #{orgId}
        GROUP BY r.id, r.role_name, r.role_code, r.description, r.status, r.updated_by, r.updated_at

    </select>
</mapper>
