<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.RoleCreateScopeMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.RoleCreateScopePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fromRoleId" column="from_role_id" jdbcType="BIGINT"/>
            <result property="toRoleId" column="to_role_id" jdbcType="BIGINT"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,from_role_id,to_role_id,
        org_id,active,created_at,
        updated_at
    </sql>
</mapper>
