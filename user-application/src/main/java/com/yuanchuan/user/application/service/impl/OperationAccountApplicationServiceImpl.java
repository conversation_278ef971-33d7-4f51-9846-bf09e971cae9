package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.authentication.content.model.UserContext;
import com.yuanchuan.authentication.content.utils.AuthUserContextUtils;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.OperationAccountDTO;
import com.yuanchuan.user.api.request.OperationAccountCreateRequest;
import com.yuanchuan.user.api.request.OperationAccountQueryRequest;
import com.yuanchuan.user.api.request.OperationAccountUpdateRequest;
import com.yuanchuan.user.application.service.OperationAccountApplicationService;
import com.yuanchuan.user.context.enums.AccountStatus;
import com.yuanchuan.user.context.enums.AccountType;
import com.yuanchuan.user.domain.model.BusinessAccount;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.repository.UserPersonRepository;
import com.yuanchuan.user.domain.service.OperationAccountDomainService;
import com.yuanchuan.user.domain.service.RoleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营账号管理应用服务实现
 */
@Slf4j
@Service
public class OperationAccountApplicationServiceImpl implements OperationAccountApplicationService {

    @Autowired
    private OperationAccountDomainService operationAccountDomainService;

    @Autowired
    private RoleDomainService roleDomainService;

    @Autowired
    private UserPersonRepository userPersonRepository;

    @Override
    public PageResult<OperationAccountDTO> queryOperationAccounts(OperationAccountQueryRequest request) {
        // 调用领域服务查询账号列表
        List<BusinessAccount> accounts = operationAccountDomainService.queryOperationAccounts(
                request.getRoleIds(),
                request.getAccountStatus(),
                request.getKeyword(),
                request
        );

        Long total = operationAccountDomainService.countOperationAccounts(
                request.getRoleIds(),
                request.getAccountStatus(),
                request.getKeyword()
        );

        // 转换为DTO
        List<OperationAccountDTO> accountDTOs = accounts.stream()
                .map(this::toOperationAccountDTO)
                .collect(Collectors.toList());

        return PageResult.<OperationAccountDTO>builder()
                .records(accountDTOs)
                .total(total)
                .pageNum(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public Long createOperationAccount(OperationAccountCreateRequest request) {
        // 获取当前操作用户
        UserContext currentUser = AuthUserContextUtils.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), "用户未登录");
        }

        // 调用领域服务创建账号
        return operationAccountDomainService.createOperationAccount(
                request.getName(),
                request.getEmployeeCode(),
                request.getEmail(),
                request.getPhone(),
                request.getRoleIds(),
                currentUser.getUsername()
        );
    }

    @Override
    public Boolean updateOperationAccount(OperationAccountUpdateRequest request) {
        // 获取当前操作用户
        UserContext currentUser = AuthUserContextUtils.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), "用户未登录");
        }

        // 调用领域服务修改账号
        return operationAccountDomainService.updateOperationAccount(
                request.getId(),
                request.getName(),
                request.getEmail(),
                request.getPhone(),
                request.getRoleIds(),
                request.getStatus(),
                currentUser.getUsername()
        );
    }

    @Override
    public OperationAccountDTO getOperationAccountById(Long id) {
        BusinessAccount account = operationAccountDomainService.getOperationAccountById(id);
        if (account == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), "账号不存在");
        }
        return toOperationAccountDTO(account);
    }

    /**
     * 转换为DTO
     */
    private OperationAccountDTO toOperationAccountDTO(BusinessAccount account) {
        OperationAccountDTO dto = new OperationAccountDTO();
        dto.setId(account.getId());
        dto.setAccountStatus(account.getAccountStatus());
        dto.setAccountStatusDesc(getAccountStatusDesc(account.getAccountStatus()));
        dto.setUpdatedBy(account.getUpdatedBy());
        dto.setCreatedAt(account.getCreatedAt());
        dto.setUpdatedAt(account.getUpdatedAt());
        dto.setEmployeeCode(account.getAccountCode());
        dto.setName(account.getAccountName());

        // 设置用户基本信息（从UserPerson获取）
        if (account.getUserPersonId() != null) {
            userPersonRepository.findById(account.getUserPersonId()).ifPresent(userPerson -> {
                dto.setEmail(userPerson.getEmail());
                dto.setPhone(userPerson.getPhone());
                // 注意：这里需要从其他地方获取姓名，因为UserPerson中没有name字段
                // 可能需要扩展UserPerson或从其他表获取
            });
        }

        // 设置角色信息
        List<Role> roles = roleDomainService.findByBusinessAccountId(account.getId());
        List<OperationAccountDTO.RoleInfo> roleInfos = roles.stream()
                .map(this::toRoleInfo)
                .collect(Collectors.toList());
        dto.setRoles(roleInfos);

        return dto;
    }

    /**
     * 转换角色信息
     */
    private OperationAccountDTO.RoleInfo toRoleInfo(Role role) {
        OperationAccountDTO.RoleInfo roleInfo = new OperationAccountDTO.RoleInfo();
        roleInfo.setRoleId(role.getId());
        roleInfo.setRoleName(role.getRoleName());
        roleInfo.setStatus(role.getStatus());
        return roleInfo;
    }

    /**
     * 获取账户状态描述
     */
    private String getAccountStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return AccountStatus.ACTIVE.getDescription();
            case 2:
                return AccountStatus.LOCKED.getDescription();
            case 3:
                return AccountStatus.DISABLED.getDescription();
            default:
                return "未知";
        }
    }
}
