package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.authentication.content.model.UserContext;
import com.yuanchuan.authentication.content.utils.AuthUserContextUtils;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.common.response.ResponseCode;
import com.yuanchuan.user.api.dto.RoleDTO;
import com.yuanchuan.user.api.dto.RoleSelectDTO;
import com.yuanchuan.user.api.request.MerchantCreateRoleRequest;
import com.yuanchuan.user.api.request.RoleCreateRequest;
import com.yuanchuan.user.api.request.RoleQueryRequest;
import com.yuanchuan.user.api.request.RoleUpdateRequest;
import com.yuanchuan.user.api.service.RoleService;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.service.RoleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 角色应用服务实现类
 */
@Slf4j
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class RoleApplicationServiceImpl implements RoleService {

    @Autowired
    private RoleDomainService roleDomainService;

    @Override
    public PageResult<RoleDTO> queryRoles(RoleQueryRequest request) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        List<Role> roles = roleDomainService.queryRoles(
                request.getRoleId(),
                request.getStatus(),
                request.getKeyword(),
                userContext.getOrgId(),
                request
        );

        Long total = roleDomainService.countRoles(
                request.getRoleId(),
                request.getStatus(),
                request.getKeyword(),
                userContext.getOrgId()
        );

        List<RoleDTO> roleDTOs = roles.stream()
                .map(this::toRoleDTO)
                .collect(Collectors.toList());

        return PageResult.<RoleDTO>builder()
                .records(roleDTOs)
                .total(total)
                .pageNum(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public List<RoleSelectDTO> getRoleSelectList() {
        // 获取系统登录用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        List<Role> roles = roleDomainService.getRoleSelectList(userContext.getOrgId());
        return roles.stream()
                .map(this::toRoleSelectDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Long createRole(RoleCreateRequest request) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Role role = new Role();
        BeanUtils.copyProperties(request, role);
        // 设置组织ID
        role.setOrgId(userContext.getOrgId());

        // 校验当前用户是否有权限创建指定的目标角色
        if (!CollectionUtils.isEmpty(request.getTargetRoleIds())) {
            List<Role> currentUserRoles = roleDomainService.findByBusinessAccountId(userContext.getBusinessAccountId());
            List<Long> currentUserRoleIds = currentUserRoles.stream()
                    .map(Role::getId)
                    .collect(Collectors.toList());

            if (!roleDomainService.validateRoleCreationPermission(currentUserRoleIds, request.getTargetRoleIds(), userContext.getOrgId())) {
                throw new BusinessException(ResponseCode.ERROR_1.getCode(), "没有权限创建指定的目标角色");
            }
        }

        Role savedRole = roleDomainService.createRole(role, request.getPermissionIds(), request.getTargetRoleIds(), userContext.getUsername());
        return savedRole.getId();
    }

    @Override
    public Boolean updateRole(RoleUpdateRequest request) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Role role = new Role();
        BeanUtils.copyProperties(request, role);

        // 校验当前用户是否有权限创建指定的目标角色
        if (!CollectionUtils.isEmpty(request.getTargetRoleIds())) {
            List<Role> currentUserRoles = roleDomainService.findByBusinessAccountId(userContext.getBusinessAccountId());
            List<Long> currentUserRoleIds = currentUserRoles.stream()
                    .map(Role::getId)
                    .collect(Collectors.toList());

            if (!roleDomainService.validateRoleCreationPermission(currentUserRoleIds, request.getTargetRoleIds(), userContext.getOrgId())) {
                throw new BusinessException(ResponseCode.ERROR_1.getCode(), "没有权限创建指定的目标角色");
            }
        }

        roleDomainService.updateRole(role, request.getPermissionIds(), request.getTargetRoleIds(), userContext.getBusinessAccountId(), userContext.getUsername());
        return true;
    }

    @Override
    public RoleDTO getRoleById(Long id) {
        // 获取系统登录用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Role role = roleDomainService.queryRoleById(id, userContext.getOrgId());
        RoleDTO dto = toRoleDTO(role);
        return dto;
    }

    @Override
    public Boolean deleteRole(Long id) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        return roleDomainService.deleteRole(id, userContext.getOrgId(), userContext.getUsername());
    }

    @Override
    public List<RoleDTO> getUserRoles(Long accountId) {
        // 获取系统登录用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        List<Role> roles = roleDomainService.getUserRoles(accountId, userContext.getOrgId());
        return roles.stream()
                .map(this::toRoleDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<RoleSelectDTO> getAllowedTargetRoles() {
        // 获取系统登录用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 获取当前用户的角色
        List<Role> currentUserRoles = roleDomainService.findByBusinessAccountId(userContext.getBusinessAccountId());
        List<Long> currentUserRoleIds = currentUserRoles.stream()
                .map(Role::getId)
                .collect(Collectors.toList());

        // 获取允许创建的目标角色ID列表
        List<Long> allowedTargetRoleIds = roleDomainService.getAllowedTargetRoleIds(currentUserRoleIds, userContext.getOrgId());

        if (CollectionUtils.isEmpty(allowedTargetRoleIds)) {
            return new ArrayList<>();
        }

        // 根据角色ID获取角色信息
        List<Role> allowedRoles = allowedTargetRoleIds.stream()
                .map(roleId -> {
                    try {
                        return roleDomainService.queryRoleById(roleId, userContext.getOrgId());
                    } catch (Exception e) {
                        log.warn("获取角色信息失败: roleId={}", roleId, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(role -> role.getStatus() == 1 && role.getActive() == 1) // 只返回启用的角色
                .collect(Collectors.toList());

        return allowedRoles.stream()
                .map(this::toRoleSelectDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean merchantCreateRole(MerchantCreateRoleRequest request) {
        if(request == null || request.hasEmptyParams()) {
            throw new BusinessException(ResponseCode.ERROR_1.getCode(), "所有参数不能为空");
        }
        return roleDomainService.merchantCreateRole(request.getMerchantId(),request.getMerchantName(),request.getOrgType(),request.getBusinessAccountId());
    }


    /**
     * 转换为RoleDTO
     */
    private RoleDTO toRoleDTO(Role role) {
        RoleDTO dto = new RoleDTO();
        BeanUtils.copyProperties(role, dto);

        if(role.getPermissionIds() != null) {
            dto.setPermissionIds(Arrays.stream(role.getPermissionIds().split(","))
                    .map(String::trim)          // 去除空格，防止输入为 "1, 2 ,3"
                    .filter(s -> !s.isEmpty())  // 防止空字符串
                    .map(Long::valueOf)         // 转为 Long 类型
                    .collect(Collectors.toList()));
        }

        if(role.getPermissionGroupDesc() != null) {
            dto.setPermissionNames(Arrays.stream(role.getPermissionGroupDesc().split(";"))
                    .map(String::trim)          // 去除空格，防止输入为 "1, 2 ,3"
                    .filter(s -> !s.isEmpty())  // 防止空字符串
                    //.map(String::valueOf)         // 转为 Long 类型
                    .collect(Collectors.toList()));
        }

        // 获取目标角色ID列表
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if (userContext != null && role.getId() != null) {
            List<Long> targetRoleIds = roleDomainService.getTargetRoleIdsByRoleId(role.getId(), userContext.getOrgId());
            dto.setTargetRoleIds(targetRoleIds);
        }

        return dto;
    }

    /**
     * 转换为RoleSelectDTO
     */
    private RoleSelectDTO toRoleSelectDTO(Role role) {
        RoleSelectDTO dto = new RoleSelectDTO();
        dto.setRoleId(role.getId());
        dto.setRoleName(role.getRoleName());
        return dto;
    }
}
